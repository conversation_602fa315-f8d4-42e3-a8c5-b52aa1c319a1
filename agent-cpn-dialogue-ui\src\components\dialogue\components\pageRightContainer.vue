<template>
    <div class="page-right-container">
        <div class="page-right-header">
            <span class="page-right-title">{{ title }}</span>
            <div class="page-right-actions">
                <!-- <div class="icons" v-for="item in icons" :key="item.name">
                    <el-tooltip :content="item.tooltip || ''" placement="top" :disabled="!item.tooltip">
                        <Icon :name="item.name" :size="14" @click="handleIconClick(item)" />
                    </el-tooltip>
                </div> -->
                <span class="divider">|</span>
                <el-icon @click="closeRightPanel"><Close /></el-icon>
            </div>
        </div>
        <div class="page-right-content">
            <slot></slot>
        </div>
    </div>
</template>

<script setup>
import { provide, reactive } from 'vue';
import { useRightPanelStore } from '@/components/dialogue/store/modules/rightPanel';

/**
 * 页面右侧容器组件
 * 作为插槽容器，用于布局管理
 */

// 定义属性
const props = defineProps({
    title: {
        type: String,
        default: '修改目录基本信息',
    },
    icons: {
        type: Array,
        default: () => [
            { name: 'copy', tooltip: '复制', handler: null },
            { name: 'fenxiang', tooltip: '分享', handler: null },
        ],
    },
});

// 定义事件
const emit = defineEmits(['icon-click']);

// 创建图标处理函数存储对象
const iconHandlers = reactive({
    // 存储格式: { iconName: [handlers] }
    handlers: {},

    // 注册处理函数
    register(iconName, handler) {
        if (!this.handlers[iconName]) {
            this.handlers[iconName] = [];
        }
        this.handlers[iconName].push(handler);
        return () => this.unregister(iconName, handler); // 返回取消注册函数
    },

    // 取消注册处理函数
    unregister(iconName, handler) {
        if (this.handlers[iconName]) {
            const index = this.handlers[iconName].indexOf(handler);
            if (index > -1) {
                this.handlers[iconName].splice(index, 1);
            }
        }
    },

    // 触发处理函数
    trigger(iconName, item) {
        if (this.handlers[iconName] && this.handlers[iconName].length) {
            this.handlers[iconName].forEach(handler => handler(item));
            return true; // 有处理函数被调用
        }
        return false; // 没有处理函数被调用
    },
});

// 提供图标处理函数对象，供子组件注入使用
provide('iconHandlers', iconHandlers);

/**
 * 处理图标点击事件
 * @param {Object} item - 图标项，包含name、tooltip和handler属性
 */
const handleIconClick = item => {
    // 如果有自定义处理函数，则调用它
    console.log('pageRightContainer-icon', item);
    if (item.handler && typeof item.handler === 'function') {
        item.handler(item);
    } else {
        // 尝试触发通过 provide/inject 注册的处理函数
        const handled = iconHandlers.trigger(item.name, item);

        // 如果没有注册的处理函数，则触发事件
        if (!handled) {
            emit('icon-click', item);
        }
    }
};

// 获取右侧面板 store
const rightPanelStore = useRightPanelStore();

// 关闭右侧面板
const closeRightPanel = () => {
    rightPanelStore.hideRightPanel();
};

// 暴露方法供父组件调用
defineExpose({
    closeRightPanel,
});
</script>

<style lang="scss" scoped>
.icons {
    cursor: pointer;
    margin-left: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.page-right-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    background-color: $bg;

    .page-right-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px;
        height: 50px;
        line-height: 50px;
        border-bottom: 1px solid #eaeaea;

        .page-right-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .page-right-actions {
            display: flex;
            align-items: center;

            .divider {
                margin: 0 8px;
                color: #ccc;
            }
        }
    }

    .page-right-content {
        flex: 1;
        overflow: auto;
        padding: 20px;
    }
}
</style>
