<template>
    <div class="answer" :class="{ 'selection-mode': isSelectionMode }">
        <!-- 选择复选框 -->
        <div class="selection-checkbox" v-if="isSelectionMode">
            <el-checkbox :model-value="isSelected" @change="handleSelectionChange" size="large" />
        </div>

        <div class="answer-content-wrapper">
            <!-- 文件列表区域 -->
            <div class="file-cards-container mb-2 mr-2 flex justify-end" v-if="localFilesList.length > 0">
                <div class="files-list">
                    <FilesCard
                        v-for="file in localFilesList"
                        :key="file.uid"
                        :uid="file.uid"
                        :name="file.name"
                        :file-type="file.fileType"
                        class="file-card"
                        :file-size="file.fileSize"
                        :hide-delete="true"
                        :url="file.url || fileUrl"
                    />
                </div>
            </div>

            <div class="answer-warp">
                <div class="answer_content">
                    <p>{{ question }}</p>
                </div>
                <div class="ml-2">
                    <el-avatar :src="userStore.avatar" :size="34" shape="circle" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { FilesCard } from 'vue-element-plus-x';
import { currentFiles } from '@/components/dialogue/store/input.js';
import { isSelectionMode, isMessageSelected, toggleMessageSelection } from '@/components/dialogue/store/input';
import { computed, inject, watch } from 'vue';
import { publicPath } from '@/components/dialogue/store/main.js';
import { useUserStore } from '@/components/dialogue/store/modules/user';

const userStore = useUserStore();

defineOptions({
    name: 'UserQuestion',
});

/**
 * 用户问题组件
 * 显示用户提问的内容和相关文件
 */

const props = defineProps({
    question: {
        type: String,
        required: true,
    },
    data: {
        type: Object,
        default: () => {},
    },
});

watch(
    () => props.data,
    v => {
        // localFilesList.value =
        //     'http://*************:9000/data-agent/upload/20250809/b3b65a2a27f91e878ef84aa52c331043.png';
    }
);
// 注入消息ID，用于选择功能
const messageId = inject('messageId', '');

// 计算是否被选中
const isSelected = computed(() => {
    return isMessageSelected(messageId);
});

// 处理选择状态变化
const handleSelectionChange = checked => {
    toggleMessageSelection(messageId);
};

// 本地存储文件列表，用于渲染
const localFilesList = ref([]);

// 组件挂载时，复制文件列表并清空全局files
onMounted(() => {
    // 保存当前文件列表的副本
    if (currentFiles.value && currentFiles.value.length > 0) {
        localFilesList.value = currentFiles.value;
        // 清空全局files
        currentFiles.value = [];
    }
});
</script>

<style lang="scss" scoped>
.answer {
    display: flex;
    margin-bottom: 16px;
    flex-direction: row;
    align-items: flex-start;
    gap: 8px;
    &.selection-mode {
        padding-left: 8px;
    }

    .selection-checkbox {
        display: flex;
        align-items: center;
        margin-top: 8px;
    }

    .answer-content-wrapper {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .answer-warp {
        display: flex;
        justify-content: end;
    }

    .answer_icon {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 40px;
        height: 40px;
        margin-left: 15px;
    }

    .answer_content {
        border-radius: 8px 0px 8px 8px;
        background: #267ef0;
        padding: 8px 12px;
        transition: all 0.2s ease;

        p {
            font-size: 14px;
            color: #fff;
            line-height: 22px;
        }
    }

    // 选择模式下的样式调整
    &.selection-mode .answer_content {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
}

.files-header {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
    color: #606266;
}

.files-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.files-card {
    width: 100%;
}
</style>
