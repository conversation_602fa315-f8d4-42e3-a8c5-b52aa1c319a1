<template>
    <thinkingContentStep :stepTitle="title">
        <template #titleSlot></template>
        <template #content>
            <!-- 查看表单内容 -->
            <div class="my_form_default">
                <table class="dl_table_style">
                    <tbody>
                        <tr>
                            <td class="common_label_title_r td_width0">执行结果：</td>
                            <td colspan="5">
                                <p class="td_block_box">
                                    <span
                                        class="td_block"
                                        :class="resultStatus.status === 'success' ? 'td_wc_color' : 'td_sb_color'"
                                    >
                                        {{ resultStatus.statusText }}
                                    </span>
                                </p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <p class="commonword">
                {{ resultStatus.summaryText }}
                <a v-if="resultStatus.showSubmitButton" @click="handleSubmit" class="commonbtn">
                    {{ resultStatus.submitButtonText }}
                </a>
            </p>
        </template>
    </thinkingContentStep>
</template>

<script setup name="mlscResult">
/**
 * 目录生成结果组件
 * @description 展示目录生成任务的执行结果，可配置显示不同状态
 */
import { computed, onMounted, reactive } from 'vue';
import thinkingContentStep from '@/components/dialogue/common/thinkingContentStep.vue';

// 定义Props
const props = defineProps({
    /**
     * 组件标题
     */
    title: {
        type: String,
        default: '目录生成结果',
    },
    /**
     * 状态文本
     */
    statusText: {
        type: String,
        default: '完成',
    },
    /**
     * 摘要文本
     */
    summaryText: {
        type: String,
        default: '目录生成完成，是否提交审核：',
    },
    /**
     * 是否显示提交按钮
     */
    showSubmitButton: {
        type: Boolean,
        default: true,
    },
    /**
     * 提交按钮文本
     */
    submitButtonText: {
        type: String,
        default: '提交',
    },
    data: {
        type: Object,
        default: () => ({}),
    },
});
const resultStatus = reactive({
    status: 'error',
    statusText: '',
    summaryText: '',
    //提交审核暂时未实现，暂时不显示提交按钮
    showSubmitButton: false,
    submitButtonText: '提交',
});
onMounted(() => {
    if (props.data && (props.data.status === true || props.data.status === '成功')) {
        resultStatus.status = 'success';
        resultStatus.statusText = '成功';
        resultStatus.summaryText = '提交审核：';
        // resultStatus.showSubmitButton = true;
        //提交审核暂时未实现，暂时不显示提交按钮
        resultStatus.showSubmitButton = false;
        resultStatus.submitButtonText = '提交';
    } else {
        resultStatus.status = 'error';
        resultStatus.statusText = '失败';
        resultStatus.summaryText = '原因：' + props.data.message;
        resultStatus.showSubmitButton = false;
        resultStatus.submitButtonText = '';
    }
});

// 定义事件
const emit = defineEmits(['submit']);

/**
 * 提交处理
 */
const handleSubmit = () => {
    emit('submit');
};
</script>

<style scoped lang="scss"></style>
