/**
 * 全站http配置
 *
 * axios参数说明
 * isSerialize是否开启form表单提交
 * isToken是否需要token
 */
import axios from 'axios';
import store from '@/store/';
import router from '@/router/';
import { serialize } from 'utils/util';
import { getToken } from 'utils/auth';
import { isURL } from 'utils/validate';
import { ElMessage } from 'element-plus';
import website from '@/config/website';
import NProgress from 'nprogress'; // progress bar
import 'nprogress/nprogress.css'; // progress bar style
import { Base64 } from 'js-base64';
import { baseUrl } from '@/config/env';
import { useUserStore } from '@/components/dialogue/store/modules/user';

import crypto from '@/utils/crypto';
axios.defaults.timeout = 10000;
//返回其他状态吗
axios.defaults.validateStatus = function (status) {
    return status >= 200 && status <= 500; // 默认的
};
//跨域请求，允许保存cookie
axios.defaults.withCredentials = true;
// NProgress Configuration
NProgress.configure({
    showSpinner: false,
});
//HTTPrequest拦截
axios.interceptors.request.use(
    config => {
        // start progress bar
        NProgress.start();
        //地址为已经配置状态则不添加前缀
        if (!isURL(config.url) && !config.url.startsWith(baseUrl)) {
            config.url = baseUrl + config.url;
        }
        //headers判断是否需要
        const authorization = config.authorization === false;
        if (!authorization) {
            config.headers['Authorization'] = `Basic ${Base64.encode(`${website.clientId}:${website.clientSecret}`)}`;
        }
        //headers判断请求是否携带token
        const meta = config.meta || {};
        const isToken = meta.isToken === false;
        //headers传递token是否加密
        const cryptoToken = config.cryptoToken === true;
        const token = getToken();
        if (token && !isToken) {
            config.headers[website.tokenHeader] = cryptoToken ? 'crypto ' + crypto.encrypt(token) : 'bearer ' + token;
        }
        //headers中配置text请求
        if (config.text === true) {
            config.headers['Content-Type'] = 'text/plain';
        }
        //headers中配置serialize为true开启序列化
        if (config.method === 'post' && meta.isSerialize === true) {
            config.data = serialize(config.data);
        }
        return config;
    },
    error => {
        return Promise.reject(error);
    }
);
//HTTPresponse拦截
axios.interceptors.response.use(
    res => {
        NProgress.done();
        const status = res.data.code || res.status;
        const statusWhiteList = website.statusWhiteList || [];
        const message = res.data.msg || res.data.error_description || '未知错误';

        // 处理401未授权错误
        if (status === 401) {
            console.log('收到401错误，开始处理token刷新...');

            // 尝试刷新token
            const userStore = useUserStore();
            if (userStore.userInfo?.refresh_token) {
                userStore
                    .refreshToken()
                    .then(() => {
                        // 刷新成功，重试原请求
                        return axios(res.config);
                    })
                    .catch(() => {
                        // 刷新失败，跳转登录页
                        store.dispatch('FedLogOut').then(() => router.push({ path: '/login' }));
                    });
                return Promise.reject(res);
            } else {
                // 没有refresh_token，直接跳转登录页
                store.dispatch('FedLogOut').then(() => router.push({ path: '/login' }));
                return Promise.reject(res);
            }
        }

        // 检查token状态（移除这个检查，因为可能导致误判）
        // const userStore = useUserStore();
        // if (!userStore.token) {
        //     router.push({ path: '/login' });
        // }

        //如果在白名单里则自行catch逻辑处理
        if (statusWhiteList.includes(status)) return Promise.reject(res);

        // 如果请求为非200否者默认统一处理
        if (status !== 200) {
            ElMessage({
                message: message,
                type: 'error',
            });
            return Promise.reject(new Error(message));
        }
        return res;
    },
    error => {
        NProgress.done();

        // 处理网络错误
        if (error.response) {
            const status = error.response.status;
            if (status === 401) {
                console.log('网络请求401错误，处理token刷新...');
                const userStore = useUserStore();
                if (userStore.userInfo?.refresh_token) {
                    userStore
                        .refreshToken()
                        .then(() => {
                            // 刷新成功，重试原请求
                            return axios(error.config);
                        })
                        .catch(() => {
                            store.dispatch('FedLogOut').then(() => router.push({ path: '/login' }));
                        });
                } else {
                    store.dispatch('FedLogOut').then(() => router.push({ path: '/login' }));
                }
            }
        }

        return Promise.reject(new Error(error));
    }
);

export default axios;
