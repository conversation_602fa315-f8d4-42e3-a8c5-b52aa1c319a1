<template>
    <div class="catalog-form" v-if="hasCatalogContent">
        <div class="catalog-tool-container">
            <p class="catalog-text">
                <img v-if="config.showIcon" :src="publicPath + `/static/dialogue/hdjiantou1.png`" class="arrow-icon" />
                <template v-for="(field, index) in visibleFields" :key="field.key">
                    <!-- 前置文本，仅当有值时显示 -->
                    <template v-if="index > 0 && getFieldText(`${field.key}Prefix`)">
                        {{ getFieldText(`${field.key}Prefix`) }}
                    </template>

                    <!-- 字段标签，仅当有值时显示 -->
                    <span v-if="getFieldText(field.key)" class="field-label">
                        <span v-if="isFieldRequired(field.key)" class="required-mark">*</span>
                        {{ getFieldText(field.key) }}
                    </span>

                    <!-- 下拉框 -->
                    <el-popover placement="bottom" :width="field.key === 'dateRange' ? 400 : 200" trigger="click">
                        <template #reference>
                            <span class="blue-text">
                                【{{ getFieldValue(field.key) || getFieldText(`${field.key}Placeholder`) }}】
                            </span>
                        </template>

                        <!-- 日期范围选择器 -->
                        <template v-if="field.key === 'dateRange'">
                            <el-date-picker
                                v-model="dateRange"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                value-format="YYYY-MM-DD"
                                style="width: 100%"
                            />
                        </template>

                        <template v-else-if="field.key === 'fileUrl'">
                            <el-input v-model="fileUrl" style="width: 100%"></el-input>
                        </template>

                        <!-- 普通下拉选择器 -->
                        <template v-else>
                            <el-select
                                v-model="fieldValues[field.key]"
                                :placeholder="placeholders[field.key] || getFieldText(`${field.key}SelectPrompt`)"
                                size="small"
                                style="width: 100%"
                            >
                                <el-option
                                    v-for="item in getOptionsForField(field.key)"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </template>
                    </el-popover>

                    <!-- 后置文本，仅当有值时显示 -->
                    <template
                        v-if="
                            (index < visibleFields.length - 1 || !config.hideEnding) &&
                            getFieldText(`${field.key}Suffix`)
                        "
                    >
                        {{ getFieldText(`${field.key}Suffix`) }}
                    </template>
                </template>
            </p>
            <div v-if="showButton" class="btn-container">
                <el-button type="primary" class="generate-btn" @click="generateTaskFun">
                    <img :src="publicPath + `/static/dialogue/task_btnimg.png`" class="btn-icon" />
                    {{ buttonText }}
                </el-button>
            </div>
        </div>
        <div class="tips" v-if="showTips">
            <span>{{ getFieldText('emptyTip') }}</span>
        </div>
    </div>
</template>

<script setup name="catalogForm">
import { computed, ref, watch } from 'vue';
import { useCatalogForm } from './composables/useCreateTaskForm';
import { publicPath } from '@/components/dialogue/store/main.js';
import { useSessionStore } from '@/components/dialogue/store/modules/session.js';
import { useUserStore } from '@/components/dialogue/store/modules/user';

const userStore = useUserStore();
const sessionStore = useSessionStore();
/**
 * @description 目录表单组件
 */
const props = defineProps({
    /**
     * 配置信息
     */
    config: {
        type: Object,
        default: () => ({
            categories: [],
            systems: [],
            dbs: [],
            schemas: [],
            tables: [],
            ranges: [],
            levels: [],
            frequencies: [],
            defaultValues: {},
            showIcon: true,
            hideFrequency: false,
            hideEnding: false,
            placeholders: {},
            fieldsConfig: [], // 字段配置数组，控制哪些字段是必填项
            texts: {}, // 自定义文本内容
            fieldOrder: [], // 自定义字段显示顺序
        }),
    },
    /**
     * 是否显示按钮
     */
    showButton: {
        type: Boolean,
        default: true,
    },
    /**
     * 按钮文本
     */
    buttonText: {
        type: String,
        default: '生成数据编目任务',
    },
    data: {
        type: Object,
        default: () => ({}),
    },
});

const emit = defineEmits(['generate-task', 'form-change', 'form-valid', 'form-invalid']);

// 将配置转换为响应式对象以便在组合式函数中使用
const configRef = ref(props.config);

// 事件发射函数
const emitEvent = (event, ...args) => {
    emit(event);
};
const fileUrlData = ref('');

const generateTaskFun = () => {
    let userId = userStore.userInfo.user_id;
    let dialogueId = sessionStore.dialogueId;
    generateTask({ dialogueId, userId });
};

// 使用封装的组合式函数
const {
    fieldValues,
    dateRange,
    fileUrl,
    showTips,
    placeholders,
    visibleFields,
    hasCatalogContent,
    getFieldValue,
    getOptionsForField,
    isFieldRequired,
    generateTask,
    resetForm,
    submitForm,
    getFormData,
    checkRequiredFields,
    getFieldText,
    sureFileUrl,
} = useCatalogForm(configRef, emitEvent, props.data);

// 暴露组件方法
defineExpose({
    resetForm,
    submitForm,
    getFormData,
    checkRequiredFields,
});
</script>

<style scoped lang="scss">
.catalog-form {
    font-size: 14px;
    width: 100%;
}

.catalog-tool-container {
    background-color: #f5f7fa;
    padding: 15px 20px;
    border-radius: 4px;
}

.catalog-text {
    line-height: 2.2;
    color: #333;
    margin-bottom: 15px;
}

.arrow-icon {
    margin-right: 5px;
    vertical-align: middle;
}

.field-label {
    display: inline-flex;
    align-items: center;

    .required-mark {
        color: #f56c6c;
        margin-right: 2px;
    }
}

.blue-text {
    color: #409eff;
    cursor: pointer;
    font-weight: normal;

    &:hover {
        opacity: 0.8;
    }
}

.btn-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    button {
        height: 28px;
        padding: 0px 16px;
        font-size: 14px;
        gap: 4px;
        background: linear-gradient(90deg, #00c8ff 0%, #1570ef 100%);
        border-radius: 14px;
    }
}

.generate-btn {
    background-color: #409eff;
    border-color: #409eff;
    padding: 8px 25px;

    &:hover,
    &:focus {
        background-color: #66b1ff;
        border-color: #66b1ff;
    }
}

.btn-icon {
    margin-right: 8px;
    vertical-align: middle;
}

.tips {
    margin-top: 10px;
    color: #f56c6c;
    font-size: 14px;
}
</style>
