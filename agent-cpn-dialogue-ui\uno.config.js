import { defineConfig, transformerVariantGroup } from 'unocss';
import presetUno from '@unocss/preset-uno';
import presetAttributify from '@unocss/preset-attributify';
import presetIcons from '@unocss/preset-icons';

export default defineConfig({
    // UnoCSS 选项
    presets: [
        // 使用内置预设
        presetUno(),
        // presetMini(),
        presetAttributify(),
        presetIcons(),
    ],
    transformers: [transformerVariantGroup()],
    shortcuts: {
        'flex-between': 'flex items-center justify-between',
        'flex-around': 'flex items-center justify-around',
        'flex-col-center': 'flex flex-col items-center justify-center',
        btn: 'px-4 py-1 rounded inline-block bg-teal-600 text-white cursor-pointer hover:bg-teal-700 disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50',
        'icon-btn':
            'text-[0.9em] inline-block cursor-pointer select-none opacity-75 transition duration-200 ease-in-out hover:opacity-100 hover:text-teal-600 !outline-none',
        'text-ellipsis': 'truncate overflow-hidden whitespace-nowrap',
        card: 'bg-white rounded-lg shadow-md p-4',
        'hover-card': 'hover:shadow-lg transition-shadow duration-300',
        input: 'border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-teal-500',
        'btn-primary': 'bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors',
        'btn-secondary': 'bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 transition-colors',
        'btn-danger': 'bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition-colors',
        'm-0-auto': 'm-0 ma', // margin: 0 auto
        'wh-full': 'w-full h-full', // width: 100%, height: 100%
        'flex-center': 'flex justify-center items-center', // flex布局居中
        'flex-x-center': 'flex justify-center', // flex布局：主轴居中
        'flex-y-center': 'flex items-center', // flex布局：交叉轴居中
        'text-overflow': 'overflow-hidden whitespace-nowrap text-ellipsis', // 文本溢出显示省略号
        'text-break': 'whitespace-normal break-all break-words', // 文本溢出换行
    },
    rules: [
        [
            /^bd-(\d+)-([a-z]+)-(#[\da-fA-F]{3,6}|(?:rgba?|hsl)a?$.*?$)$/,
            ([_, width, style, color]) => ({
                'border-width': `${width}px`,
                'border-style': style,
                'border-color': color,
            }),
            /^text-overflow-(\d+)$/,
            ([_, d]) =>
                d === '1'
                    ? { 'white-space': 'nowrap', overflow: 'hidden', 'text-overflow': 'ellipsis' }
                    : {
                          display: '-webkit-box',
                          '-webkit-line-clamp': d,
                          'line-clamp': d,
                          '-webkit-box-orient': 'vertical',
                          'box-orient': 'vertical',
                          overflow: 'hidden',
                          'text-overflow': 'ellipsis',
                      },
        ],
    ],
    theme: {
        colors: {
            // 可以在这里扩展颜色
            white: {
                50: '#f9fafb',
            },
            black: {
                50: '#000',
            },
        },
    },
});
