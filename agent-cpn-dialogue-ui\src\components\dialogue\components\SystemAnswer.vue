<template>
    <div class="ask" :class="{ 'selection-mode': isSelectionMode }">
        <!-- 选择复选框 -->
        <div class="selection-checkbox" v-if="isSelectionMode">
            <el-checkbox :model-value="isSelected" @change="handleSelectionChange" size="large" />
        </div>

        <div class="ask-content-wrapper">
            <el-avatar
                :src="publicPath + `/static/dialogue/ask_icon.png`"
                :size="40"
                fit="fit"
                shape="circle"
                class="mr-2"
            />

            <div class="ask_content">
                <!-- 动态渲染组件 -->
                <template v-for="(comp, idx) in components" :key="idx">
                    <component
                        :is="getComponent(comp.name)"
                        v-bind="{ ...comp.props }"
                        v-on="comp.events || {}"
                    ></component>
                </template>
            </div>
        </div>
    </div>
    <div class="feedback-container" v-if="answerProps.showFeedback">
        <div class="feedback-wrapper">
            <Icon name="copy" @click="handleCopy" class="copy-icon" />
            <div class="divider-line"></div>

            <span class="feedback-title">这个回答正确吗？</span>
            <div class="icon-container">
                <Icon
                    name="dianzan"
                    @click="handleFeedback('like')"
                    :class="['feedback-icon', { active: feedbackStatus === 'like' }]"
                />
                <Icon
                    name="dianzan2"
                    @click="handleFeedback('dislike')"
                    :class="['feedback-icon', { active: feedbackStatus === 'dislike' }]"
                />
            </div>
        </div>

        <!-- 问题推荐部分 -->
        <problem-recommend
            v-if="answerProps.showProblemRecommend"
            :problemList="answerProps.problemsData"
            @problem-click="handleProblemClick"
        ></problem-recommend>
    </div>
</template>

<script setup>
/**
 * 系统回答组件
 * 显示系统回答的内容，支持多种回答类型
 */
import { inject, computed, ref } from 'vue';
import ProblemRecommend from '@/components/dialogue/common/ProblemRecommend.vue';
import { ElMessage } from 'element-plus';
import { isSelectionMode, isMessageSelected, toggleMessageSelection } from '@/components/dialogue/store/input';
import { publicPath } from '@/components/dialogue/store/main.js';
// import ava form '/static/dialogue/ask_icon.png'
defineOptions({
    name: 'SystemAnswer',
});

// 定义组件props
const props = defineProps({
    /**
     * 要渲染的组件配置数组，每个元素包含name和props
     */
    components: {
        type: Array,
        default: () => [],
    },
    /**
     * 消息内容
     */
    message: {
        type: String,
        default: '',
    },
    showProblemRecommend: {
        type: Boolean,
        default: true,
    },
    showFeedback: {
        type: Boolean,
        default: true,
    },

    /**
     * answer回答容器的属性
     */
    answerProps: {
        type: Object,
        default: () => ({
            showProblemRecommend: false,
            showFeedback: false,
            problemsData: [
                { id: '1', title: '生成关联指标看板' },
                { id: '2', title: '5月佛山市办件量TOP10单位' },
                { id: '3', title: '一市五区各渠道5月数据采集情况' },
            ],
        }),
    },
});

// 定义事件
const emit = defineEmits(['feedback', 'problem-click']);

// 注入组件映射表
const componentMap = inject('componentMap', {});

// 注入消息ID，用于选择功能
const messageId = inject('messageId', '');

// 反馈状态：null, 'like', 'dislike'
const feedbackStatus = ref(null);

// 计算是否被选中
const isSelected = computed(() => {
    return isMessageSelected(messageId);
});

// 处理选择状态变化
const handleSelectionChange = checked => {
    toggleMessageSelection(messageId);
};

/**
 * 获取组件实例
 * @param {string} name 组件名称
 * @returns {Component} 组件实例
 */
const getComponent = name => {
    return componentMap[name] || null;
};

/**
 * 处理反馈点击事件
 * @param {string} type 反馈类型：'like' 或 'dislike'
 */
const handleFeedback = type => {
    // 如果点击的是当前已选状态，则取消选择
    if (feedbackStatus.value === type) {
        feedbackStatus.value = null;
    } else {
        feedbackStatus.value = type;
    }

    // 向父组件发送反馈事件
    emit('feedback', {
        type: feedbackStatus.value,
        timestamp: new Date().getTime(),
    });
};

/**
 * 处理复制内容事件
 */
const handleCopy = () => {
    // 获取回答内容
    let content = '';
    try {
        // 尝试获取所有文本内容
        const textElements = document.querySelectorAll('.ask_content .text-message');
        textElements.forEach(el => {
            content += el.textContent + '\n';
        });

        // 如果没有找到内容，尝试获取整个回答区域的内容
        if (!content) {
            content = document.querySelector('.ask_content').textContent;
        }

        // 复制到剪贴板
        navigator.clipboard
            .writeText(content.trim())
            .then(() => {
                ElMessage({
                    message: '内容已复制到剪贴板',
                    type: 'success',
                    duration: 2000,
                });
            })
            .catch(err => {
                console.error('复制失败:', err);
                ElMessage({
                    message: '复制失败，请手动复制',
                    type: 'error',
                    duration: 2000,
                });
            });
    } catch (error) {
        console.error('复制操作出错:', error);
        ElMessage({
            message: '复制失败，请手动复制',
            type: 'error',
            duration: 2000,
        });
    }
};

/**
 * 处理问题点击事件
 * @param {Object} problem 问题对象
 */
const handleProblemClick = problem => {
    emit('problem-click', problem);
};
</script>

<style lang="scss" scoped>
.ask {
    display: flex;
    margin-bottom: 20px;
    align-items: flex-start;
    gap: 8px;
    width: 100%;

    &.selection-mode {
        padding-left: 8px;
    }

    .selection-checkbox {
        display: flex;
        align-items: center;
        margin-top: 8px;
    }

    .ask-content-wrapper {
        flex: 1;
        display: flex;
    }

    .ask_icon {
        width: 40px;
        height: 40px;
        flex-shrink: 0;

        img {
            width: 100%;
            height: 100%;
        }
    }

    .ask_content {
        flex: 1;
        overflow: hidden;
        transition: all 0.2s ease;
    }

    .text-message {
        font-size: 14px;
        line-height: 1.5;
        white-space: pre-wrap;
    }

    // 选择模式下的样式调整
    &.selection-mode .ask_content {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        padding: 8px;
    }
}

.feedback-container {
    padding-left: 50px;
    margin-bottom: 20px;

    .feedback-wrapper {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .divider-line {
            width: 1px;
            height: 16px;
            background-color: #dcdfe6;
            margin: 0 12px;
        }
    }

    .feedback-title {
        font-size: 14px;
        color: #606266;
        margin-right: 12px;
    }

    .icon-container {
        display: flex;
        gap: 16px;
    }

    .copy-icon {
        cursor: pointer;
        color: #606266;

        &:hover {
            color: #409eff;
        }
    }

    .feedback-icon {
        cursor: pointer;
        transition: all 0.3s;
        color: #606266;

        &:hover {
            transform: scale(1.1);
        }

        &.active {
            color: #409eff;
            transform: scale(1.1);
        }
    }
}
</style>
