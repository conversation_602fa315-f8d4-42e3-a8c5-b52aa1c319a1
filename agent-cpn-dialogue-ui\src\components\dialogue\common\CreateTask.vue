<template>
    <div class="catalog-generator">
        <thinkingContentStep :stepTitle="title" :lastStep="true">
            <template #titleSlot>
                <slot name="title-content"></slot>
            </template>
            <template #content>
                <CatalogForm
                    :config="dataConfig"
                    :showButton="showButton"
                    :buttonText="buttonText"
                    :data="props.data"
                    @generate-task="handleGenerateTask"
                    @form-invalid="handleFormInvalid"
                />
            </template>
            <template #secondSlot>
                <slot name="result-content"></slot>
            </template>
        </thinkingContentStep>
    </div>
</template>

<script setup name="createTask">
import { ref, defineExpose, reactive, onMounted } from 'vue';
import CatalogForm from './CatalogForm.vue';
import thinkingContentStep from './thinkingContentStep.vue';
import { useDialogueHandler } from '@/components/dialogue/hooks/useDialogueHandler';
import { generateDialogueMsg, reply } from '@/api/dialogue';
import { useRightPanelStore } from '@/components/dialogue/store/modules/rightPanel';
import { DialogueService } from '@/components/dialogue/hooks/useSystemResponse';
import { getDictionary } from '@/api/system/dict';
import { ElMessage } from 'element-plus';
import { selectAppSystem, selectDatabaseApi, selectFileSystemList } from '@/api/dialogue/data-catalog-util-api';
import { selectServiceList } from '@/api/dialogue/data-service-util-api';

// 获取右侧面板控制函数
const { setRightPanel, hideRightPanel } = useRightPanelStore();
// 获取对话处理函数
const { replySendWs } = useDialogueHandler();
/**
 * @description 目录生成工具组件
 */
const props = defineProps({
    /**
     * 组件标题
     */
    title: {
        type: String,
        default: '资源类别信息配置',
    },
    /**
     * 配置信息
     */
    config: {
        type: Object,
        default: () => ({}),
    },
    /**
     * 是否显示按钮
     */
    showButton: {
        type: Boolean,
        default: true,
    },
    /**
     * 按钮文本
     */
    buttonText: {
        type: String,
        default: '生成数据编目任务',
    },
    data: {
        type: Object,
        default: () => ({}),
    },
});
onMounted(async () => {
    dataConfig.categories = await getDict('catalog_type');
    dataConfig.systems = await getSystem();
    dataConfig.dbs = await selectDatabase();
    dataConfig.services = await selectServices();
    dataConfig.fileSystems = await selectFileSystems();
    dataConfig.ranges = await getDict('data_range');
    dataConfig.levels = await getDict('data_level');
});
const dictData = reactive({
    categories: [],
});
/**
 * 字典获取
 */
const getDict = async key => {
    let dict = [];
    try {
        const res = await getDictionary({ code: key });
        res.data.data.forEach(item => {
            dict.push({
                label: item.dictValue,
                value: Number(item.dictKey),
            });
        });
        return dict;
    } catch (error) {
        console.error(`Failed to fetch ${key}:`, error);
    }
};
/**
 * 字典获取
 */
const getSystem = async () => {
    let dict = [];
    try {
        const res = await selectAppSystem();
        if (res.data.data) {
            res.data.data.forEach(item => {
                dict.push({
                    label: item.name,
                    value: item.id,
                });
            });
        }
        return dict;
    } catch (error) {
        console.error(`Failed to fetch :`, error);
    }
};
/**
 * 字典获取
 */
const selectDatabase = async () => {
    let dict = [];
    try {
        const res = await selectDatabaseApi();
        if (res.data.data) {
            res.data.data.forEach(item => {
                dict.push({
                    label: item.name,
                    value: item.id,
                });
            });
        }
        return dict;
    } catch (error) {
        console.error(`Failed to fetch :`, error);
    }
};
const selectServices = async () => {
    let dict = [];
    try {
        const res = await selectServiceList();
        console.log('res.data.data', res);
        if (res.data.data) {
            res.data.data.forEach(item => {
                dict.push({
                    label: item.name,
                    value: item.id,
                });
            });
        }
        return dict;
    } catch (error) {
        console.error(`Failed to fetch :`, error);
    }
};
const selectFileSystems = async () => {
    let dict = [];
    try {
        const res = await selectFileSystemList();
        console.log('res.data.data', res);
        if (res.data.data) {
            res.data.data.forEach(item => {
                dict.push({
                    label: item.name,
                    value: item.id,
                });
            });
        }
        return dict;
    } catch (error) {
        console.error(`Failed to fetch :`, error);
    }
};

const emit = defineEmits(['generate-task', 'update:config']);

const Texts = {
    // 字段标签
    category: '该资源目录是',
    system: '应用系统是',
    db: '来源数据库是',
    schema: '模式名称',
    table: '数据表是',
    range: '数据范围',
    level: '数据分级',
    dateRange: '时间范围',
    frequency: '更新频率',
    fileSystem: '文件服务器',
    fileUrl: '文件路径',
    service: '服务接口',

    // 字段前缀
    categoryPrefix: '',
    systemPrefix: '，',
    dbPrefix: '，',
    schemaPrefix: ' ',
    tablePrefix: '，',
    rangePrefix: '，',
    levelPrefix: '，',
    dateRangePrefix: '，',
    frequencyPrefix: '，',

    // 字段后缀
    categorySuffix: '类别',
    systemSuffix: '',
    dbSuffix: '',
    schemaSuffix: '',
    tableSuffix: '',
    rangeSuffix: '',
    levelSuffix: '',
    dateRangeSuffix: '',
    frequencySuffix: '。',
    fileSystemSuffix: '',
    fileUrlSuffix: '',
    serviceSuffix: '',

    // 占位符
    categoryPlaceholder: '类别名',
    systemPlaceholder: '系统名',
    dbPlaceholder: '数据库',
    schemaPlaceholder: '模式',
    tablePlaceholder: '表名',
    rangePlaceholder: '范围',
    levelPlaceholder: '级别',
    dateRangePlaceholder: '2024-01-01—2024-12-31',
    frequencyPlaceholder: '频率',
    fileSystemPlaceholder: '文件服务器',
    fileUrlPlaceholder: '文件路径',
    servicePlaceholder: '接口地址',

    // 选择提示
    categorySelectPrompt: '请选择类别',
    systemSelectPrompt: '请选择系统',
    dbSelectPrompt: '请选择数据库',
    schemaSelectPrompt: '请选择模式',
    tableSelectPrompt: '请选择数据表',
    rangeSelectPrompt: '请选择范围',
    levelSelectPrompt: '请选择级别',
    frequencySelectPrompt: '请选择频率',
    fileSystemSelectPrompt: '请选择文件服务器',
    serviceSelectPrompt: '请选择服务接口',

    // 其他
    emptyTip: '必填项为空，请补充相关信息。',
};

const dataConfig = reactive({
    // 显示设置

    // 自定义文本内容
    texts: Texts,

    // 自定义字段顺序
    fieldOrder: ['category', 'system', 'db', 'schema', 'table', 'range', 'level', 'dateRange'],

    // 必填字段配置
    fieldsConfig: [
        { key: 'category', label: '目录分类', required: true },
        { key: 'system', label: '应用系统', required: true },
        { key: 'db', label: '来源数据库', required: true },
        { key: 'schema', label: '模式名称', required: false },
        { key: 'table', label: '数据表是', required: true },
        { key: 'range', label: '数据范围', required: false },
        { key: 'level', label: '数据分级', required: false },
        { key: 'dateRange', label: '时间范围', required: false },
        { key: 'fileSystem', label: '文件系统', required: false },
        { key: 'fileUrl', label: '文件URL', required: false },
        { key: 'service', label: '服务名称', required: false },
    ],

    // 数据源选项
    categories: [],

    // 数据库选项
    systems: [],

    // 数据库选项
    dbs: [],

    schemas: [],

    // 数据表选项
    tables: [],

    // 数据范围选项
    ranges: [],

    // 目标数据源（使用level字段表示）
    levels: [],

    services: [],

    fileSystems: [],

    // 目标数据库（使用frequency字段表示）
    // frequencies: [
    //   { label: 'integrated_db', value: 'integrated_db' },
    //   { label: 'warehouse_db', value: 'warehouse_db' },
    //   { label: 'analytics_db', value: 'analytics_db' },
    // ],

    // 目标表选项
    targetTables: [
        { label: 'target_policies', value: 'target_policies' },
        { label: 'integrated_policies', value: 'integrated_policies' },
    ],

    // 默认值
    defaultValues: {
        category: '',
        system: '',
        db: '',
        schema: '',
        table: '',
        range: '',
        level: '',
        fileUrl: '',
        frequency: '',
    },

    // 自定义占位符
    placeholders: {
        category: '选择源数据源',
        system: '选择源数据库',
        table: '选择源数据表',
        range: '选择数据范围',
        level: '选择目标数据源',
        frequency: '选择目标数据库',
        targetTable: '选择目标数据表',
    },
});
// 任务生成状态
const taskGenerated = ref(false);

const handleFormInvalid = () => {
    // console.log('表单验证失败')
    // ElMessage.error('请填写完整信息');
};

// 处理任务生成
const handleGenerateTask = async data => {};

// 暴露重置方法
const reset = () => {
    taskGenerated.value = false;
};

// 暴露组件方法
defineExpose({
    reset,
    taskGenerated,
});
</script>

<style scoped lang="scss">
.catalog-generator {
    width: 100%;
}
</style>
