import { defineStore } from 'pinia';
import { useRouter } from 'vue-router';
import { ref } from 'vue';
import { removeToken, removeRefreshToken } from 'utils/auth';
import { ElMessageBox } from 'element-plus';
import { useSessionStore } from './session';
import { publicPath } from '@/components/dialogue/store/main.js';
import { refreshToken as apiRefreshToken } from '@/api/user';

export const useUserStore = defineStore(
    'user',
    () => {
        const token = ref();
        const router = useRouter();
        const session = useSessionStore();
        // vuex
        const store = useStore();

        const setToken = value => {
            token.value = value;
        };
        const clearToken = () => {
            token.value = void 0;
        };

        const userInfo = ref();
        const setUserInfo = value => {
            userInfo.value = value;
        };
        const clearUserInfo = () => {
            userInfo.value = void 0;
        };
        const avatar = computed(() => {
            if (userInfo.value.avatar.startsWith(import.meta.env.BASE_URL)) {
                return userInfo.value.avatar;
            } else {
                return import.meta.env.BASE_URL + userInfo.value.avatar;
            }
        });
        // const logout = async () => {
        //     // 如果需要调用接口，可以在这里调用
        //     clearToken();
        //     clearUserInfo();
        //     removeToken();
        //     removeRefreshToken();
        //     router.push('/login');
        // };
        const logout = () => {
            ElMessageBox.confirm('退出系统, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                store.dispatch('LogOut').then(() => {
                    clearToken();
                    clearUserInfo();
                    session.clearSession();
                    router.push({ path: '/login' });
                });
            });
        };
        const toIndex = async () => {
            session.createSessionBtn('index');
        };
        /**
         * @description 跳转到测试页面
         */
        const toCeshi = () => {
            router.push({ path: '/wel/index' });
        };

        const login = () => {
            router.push({
                name: '登录页',
                // params: {
                //     isLogin: 'register',
                // },
                query: { redirect: router.currentRoute.value.fullPath },
            });
        };
        /**
         * @description 刷新token
         */
        const refreshToken = async () => {
            try {
                const refresh_token = userInfo.value?.refresh_token;
                if (!refresh_token) {
                    throw new Error('无refresh_token');
                }

                console.log('开始刷新token...');
                const res = await apiRefreshToken(
                    refresh_token,
                    userInfo.value.tenant_id,
                    userInfo.value.dept_id,
                    userInfo.value.role_id
                );

                const data = res.data;
                if (data.access_token) {
                    setToken(data.access_token);
                    setUserInfo(data);

                    // 同步更新Vuex store
                    store.commit('SET_TOKEN', data.access_token);
                    if (data.refresh_token) {
                        store.commit('SET_REFRESH_TOKEN', data.refresh_token);
                    }
                    store.commit('SET_USER_INFO', data);

                    console.log('Token刷新成功');
                    return data;
                } else {
                    throw new Error('刷新token响应数据异常');
                }
            } catch (error) {
                console.error('刷新token失败:', error);
                // 刷新失败，清除所有状态
                clearToken();
                clearUserInfo();
                removeToken();
                removeRefreshToken();
                throw error;
            }
        };
        return {
            token,
            userInfo,
            setToken,
            clearToken,
            setUserInfo,
            toCeshi,
            clearUserInfo,
            logout,
            login,
            toIndex,
            avatar,
            refreshToken,
        };
    },
    {
        persist: true,
    }
);
